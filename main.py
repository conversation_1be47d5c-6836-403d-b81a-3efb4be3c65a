import os
import time
import requests
import speech_recognition as sr

# Configuration: set these environment variables before running
env_token = os.getenv("pk_67325524_CW5QVBF4FRE4461PFDE0SOLMHB0OI2IS")
env_team_id = os.getenv("90161110432")  # ClickUp Team (Workspace) ID

if not env_token or not env_team_id:
    raise EnvironmentError(
        "Please set CLICKUP_TOKEN and CLICKUP_TEAM_ID environment variables."
    )

CLICKUP_BASE_URL = "https://api.clickup.com/api/v2"
HEADERS = {
    "Authorization": env_token,
    "Content-Type": "application/json"
}


def fetch_lists():
    """
    Fetch all lists in the specified team (workspace).
    Returns a dict mapping list names (lowercased) to their IDs.
    """
    url = f"{CLICKUP_BASE_URL}/team/{env_team_id}/list"
    resp = requests.get(url, headers=HEADERS)
    resp.raise_for_status()
    data = resp.json().get('lists', [])
    return {lst['name'].lower(): lst['id'] for lst in data}


def create_clickup_task(list_id: str, task_name: str, task_desc: str = "") -> dict:
    """
    Create a new task in ClickUp under the given list ID.
    """
    url = f"{CLICKUP_BASE_URL}/list/{list_id}/task"
    payload = {"name": task_name, "description": task_desc}
    resp = requests.post(url, headers=HEADERS, json=payload)
    resp.raise_for_status()
    return resp.json()


def listen_and_create():
    recognizer = sr.Recognizer()
    mic = sr.Microphone()
    print("🎤 Calibrating microphone...")
    with mic as source:
        recognizer.adjust_for_ambient_noise(source, duration=1)
    print("✅ Ready! Say your command in the format: 'Task name to List Name'.")

    # Preload lists
    lists = fetch_lists()
    print(f"🔍 Found lists: {', '.join(lists.keys())}")

    try:
        while True:
            with mic as source:
                print("Listening...")
                audio = recognizer.listen(source)

            try:
                text = recognizer.recognize_google(audio)
                print(f"📝 Heard: {text}")

                # Parse command
                if ' to ' in text.lower():
                    task_part, list_part = text.lower().split(' to ', 1)
                    list_name = list_part.strip()
                    task_name = task_part.strip().capitalize()

                    if list_name in lists:
                        list_id = lists[list_name]
                        result = create_clickup_task(list_id, task_name)
                        print(f"✅ Created task '{task_name}' in list '{list_name}'. ID: {result.get('id')}")
                    else:
                        print(f"⚠️ List '{list_name}' not found. Available lists: {', '.join(lists.keys())}")
                else:
                    print("⚠️ Command format incorrect. Use 'Task name to List Name'.")

            except sr.UnknownValueError:
                print("⚠️ Could not understand audio.")
            except sr.RequestError as e:
                print(f"❌ Speech API error: {e}")
            except requests.HTTPError as e:
                print(f"❌ ClickUp API error: {e.response.text}")

            time.sleep(1)
    except KeyboardInterrupt:
        print("👋 Stopped. Bye!")


if __name__ == "__main__":
    listen_and_create()
